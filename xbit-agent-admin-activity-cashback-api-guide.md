# XBIT Agent Admin Activity Cashback API Guide

## Overview

The XBIT Agent Activity Cashback system provides a comprehensive rewards program where users earn points through various activities (trading, community engagement, daily check-ins) and receive cashback based on their tier level. This guide covers all admin APIs for managing the system.

### System Architecture
- **Tier Benefits**: Define reward tiers with cashback percentages and requirements
- **Categories**: Organize tasks into logical groups (DAILY, COMMUNITY, TRADING)
- **Tasks**: Individual activities users can complete to earn points
- **User Progress**: Track user completion status and points earned

## Authentication

All admin APIs require **API Key authentication** using the `X-API-Key` header.

### Required Headers
```http
X-API-Key: your-internal-api-key
Content-Type: application/json
```

### Admin GraphQL Endpoint
```
POST /api/dex-agent/admin/graphql
```

## Tier Benefits Management

### 1. Get All Tier Benefits

**Query:**
```graphql
query GetAllTierBenefits($input: AllTierBenefitsInput!) {
  adminGetAllTierBenefits(input: $input) {
    data {
      id
      tierLevel
      tierName
      minPoints
      cashbackPercentage
      referredIncentivePercentage
      netFee
      benefitsDescription
      tierColor
      tierIcon
      isActive
      createdAt
      updatedAt
    }
    total
    page
    pageSize
    totalPages
  }
}
```

**Variables:**
```json
{
  "input": {
    "page": 1,
    "pageSize": 10,
    "sortBy": "tierLevel",
    "sortOrder": "ASC"
  }
}
```

**Response:**
```json
{
  "data": {
    "adminGetAllTierBenefits": {
      "data": [
        {
          "id": "1",
          "tierLevel": 1,
          "tierName": "Bronze",
          "minPoints": 0,
          "cashbackPercentage": 0.001,
          "referredIncentivePercentage": 0.05,
          "netFee": 0.0005,
          "benefitsDescription": "Entry level benefits",
          "tierColor": "#CD7F32",
          "tierIcon": "🥉",
          "isActive": true,
          "createdAt": "2024-01-01T00:00:00Z",
          "updatedAt": "2024-01-01T00:00:00Z"
        }
      ],
      "total": 5,
      "page": 1,
      "pageSize": 10,
      "totalPages": 1
    }
  }
}
```

### 2. Create Tier Benefit

**Mutation:**
```graphql
mutation CreateTierBenefit($input: CreateTierBenefitInput!) {
  createTierBenefit(input: $input) {
    success
    message
    data {
      id
      tierLevel
      tierName
      minPoints
      cashbackPercentage
      referredIncentivePercentage
      netFee
      benefitsDescription
      tierColor
      tierIcon
      isActive
    }
  }
}
```

**Variables:**
```json
{
  "input": {
    "tierLevel": 2,
    "tierName": "Silver",
    "minPoints": 1000,
    "cashbackPercentage": 0.002,
    "referredIncentivePercentage": 0.05,
    "netFee": 0.0008,
    "benefitsDescription": "Enhanced cashback and lower fees",
    "tierColor": "#C0C0C0",
    "tierIcon": "🥈"
  }
}
```

**Response:**
```json
{
  "data": {
    "createTierBenefit": {
      "success": true,
      "message": "Tier benefit created successfully",
      "data": {
        "id": "2",
        "tierLevel": 2,
        "tierName": "Silver",
        "minPoints": 1000,
        "cashbackPercentage": 0.002,
        "referredIncentivePercentage": 0.05,
        "netFee": 0.0008,
        "benefitsDescription": "Enhanced cashback and lower fees",
        "tierColor": "#C0C0C0",
        "tierIcon": "🥈",
        "isActive": true
      }
    }
  }
}
```

### 3. Update Tier Benefit

**Mutation:**
```graphql
mutation UpdateTierBenefit($input: UpdateTierBenefitInput!) {
  updateTierBenefit(input: $input) {
    success
    message
    data {
      id
      tierLevel
      tierName
      minPoints
      cashbackPercentage
      referredIncentivePercentage
      netFee
      benefitsDescription
      tierColor
      tierIcon
      isActive
    }
  }
}
```

**Variables:**
```json
{
  "input": {
    "id": "2",
    "cashbackPercentage": 0.0025,
    "netFee": 0.0007,
    "benefitsDescription": "Updated benefits description"
  }
}
```

### 4. Delete Tier Benefit

**Mutation:**
```graphql
mutation DeleteTierBenefit($id: ID!) {
  deleteTierBenefit(id: $id)
}
```

**Variables:**
```json
{
  "id": "2"
}
```

**Response:**
```json
{
  "data": {
    "deleteTierBenefit": true
  }
}
```

## Task Categories Management

### 1. Get All Categories

**Query:**
```graphql
query GetAllCategories($input: AllTaskCategoriesInput!) {
  adminGetAllCategories(input: $input) {
    data {
      id
      name
      displayName
      description
      icon
      isActive
      sortOrder
      createdAt
      updatedAt
    }
    total
    page
    pageSize
    totalPages
  }
}
```

**Variables:**
```json
{
  "input": {
    "page": 1,
    "pageSize": 10,
    "sortBy": "sortOrder",
    "sortOrder": "ASC"
  }
}
```

### 2. Create Task Category

**Mutation:**
```graphql
mutation CreateTaskCategory($input: CreateTaskCategoryInput!) {
  createTaskCategory(input: $input) {
    id
    name
    displayName
    description
    icon
    isActive
    sortOrder
  }
}
```

**Variables:**
```json
{
  "input": {
    "name": "DAILY",
    "displayName": "Daily Tasks",
    "description": "Tasks that reset daily",
    "icon": "📅",
    "sortOrder": 1
  }
}
```

### 3. Update Task Category

**Mutation:**
```graphql
mutation UpdateTaskCategory($input: UpdateTaskCategoryInput!) {
  updateTaskCategory(input: $input) {
    id
    name
    displayName
    description
    icon
    isActive
    sortOrder
  }
}
```

**Variables:**
```json
{
  "input": {
    "id": "1",
    "displayName": "Updated Daily Tasks",
    "description": "Updated description",
    "isActive": true
  }
}
```

### 4. Delete Task Category

**Mutation:**
```graphql
mutation DeleteTaskCategory($id: ID!) {
  deleteTaskCategory(id: $id)
}
```

## Task Management

### Available Task Types

#### Task Frequencies
- `DAILY`: Resets every day at UTC 00:00
- `ONE_TIME`: Can only be completed once
- `UNLIMITED`: Can be completed multiple times
- `PROGRESSIVE`: Milestone-based progression
- `MANUAL`: Requires manual verification

#### Task Identifiers
- **Daily Tasks**: `DAILY_CHECKIN`, `MEME_TRADE_DAILY`, `PERPETUAL_TRADE_DAILY`, `MARKET_PAGE_VIEW`, `CHECK_MARKET_TRENDS`
- **Community Tasks**: `TWITTER_FOLLOW`, `TWITTER_RETWEET`, `TWITTER_LIKE`, `TELEGRAM_JOIN`, `INVITE_FRIENDS`, `SHARE_REFERRAL`, `SHARE_EARNINGS_CHART`
- **Trading Tasks**: `TRADING_POINTS`

#### Task Categories
- `DAILY`: Daily recurring tasks
- `COMMUNITY`: Social media and referral tasks
- `TRADING`: Trading-related activities

### 1. Get All Tasks

**Query:**
```graphql
query GetAllTasks {
  adminGetAllTasks {
    id
    categoryId
    category {
      id
      name
      displayName
    }
    name {
      en
      zh
      ja
      hi
      hk
      vi
    }
    description
    frequency
    taskIdentifier
    points
    maxCompletions
    resetPeriod
    conditions
    actionTarget
    verificationMethod
    externalLink
    taskIcon
    buttonText
    startDate
    endDate
    sortOrder
    isActive
    createdAt
    updatedAt
  }
}
```

### 2. Create Standard Task

**Mutation:**
```graphql
mutation CreateTask($input: CreateTaskInput!) {
  createTask(input: $input) {
    id
    categoryId
    category {
      name
      displayName
    }
    name {
      en
      zh
      ja
      hi
      hk
      vi
    }
    description
    frequency
    taskIdentifier
    points
    maxCompletions
    resetPeriod
    conditions
    actionTarget
    verificationMethod
    externalLink
    taskIcon
    buttonText
    startDate
    endDate
    sortOrder
    isActive
  }
}
```

**Variables:**
```json
{
  "input": {
    "categoryId": "1",
    "name": {
      "en": "Daily Login Bonus",
      "zh": "每日登录奖励",
      "ja": "デイリーログインボーナス"
    },
    "description": "Login daily to earn points",
    "frequency": "DAILY",
    "taskIdentifier": "DAILY_CHECKIN",
    "points": 10,
    "maxCompletions": 1,
    "resetPeriod": "daily",
    "actionTarget": "login",
    "verificationMethod": "auto",
    "taskIcon": "🎁",
    "buttonText": "Claim",
    "sortOrder": 1
  }
}
```

### 3. Create Consecutive Check-in Task

**Mutation:**
```graphql
mutation CreateConsecutiveCheckinTask($input: CreateConsecutiveCheckinTaskInput!) {
  createConsecutiveCheckinTask(input: $input) {
    id
    categoryId
    category {
      name
      displayName
    }
    name {
      en
      zh
      ja
      hi
      hk
      vi
    }
    description
    frequency
    taskIdentifier
    points
    conditions
    taskIcon
    buttonText
    isActive
  }
}
```

**Variables:**
```json
{
  "input": {
    "milestones": [
      {"days": 3, "points": 50},
      {"days": 7, "points": 200},
      {"days": 30, "points": 1000}
    ],
    "categoryId": "1",
    "name": {
      "en": "Consecutive Check-in Rewards",
      "zh": "连续签到奖励"
    },
    "description": "Earn bonus points for consecutive daily check-ins",
    "taskIcon": "🔥",
    "buttonText": "Check In",
    "sortOrder": 1
  }
}
```

### 4. Create Accumulated MEME Trading Volume Task

**Mutation:**
```graphql
mutation CreateAccumulatedMEMETradingVolumeTask($input: CreateAccumulatedMEMETradingVolumeTaskInput!) {
  createAccumulatedMEMETradingVolumeTask(input: $input) {
    id
    categoryId
    category {
      name
      displayName
    }
    name {
      en
      zh
      ja
      hi
      hk
      vi
    }
    description
    frequency
    taskIdentifier
    points
    conditions
    taskIcon
    buttonText
    isActive
  }
}
```

**Variables:**
```json
{
  "input": {
    "volumeThreshold": 1000.0,
    "points": 500,
    "categoryId": "3",
    "name": {
      "en": "MEME Trading Volume Milestone",
      "zh": "MEME交易量里程碑"
    },
    "description": "Reach $1000 in MEME trading volume",
    "taskIcon": "💰",
    "buttonText": "Trade",
    "sortOrder": 1
  }
}
```

### 5. Update Task

**Mutation:**
```graphql
mutation UpdateTask($input: UpdateTaskInput!) {
  updateTask(input: $input) {
    id
    categoryId
    name {
      en
      zh
      ja
      hi
      hk
      vi
    }
    description
    frequency
    taskIdentifier
    points
    maxCompletions
    resetPeriod
    conditions
    actionTarget
    verificationMethod
    externalLink
    taskIcon
    buttonText
    startDate
    endDate
    sortOrder
    isActive
  }
}
```

**Variables:**
```json
{
  "input": {
    "id": "task-uuid-here",
    "points": 15,
    "description": "Updated task description",
    "isActive": true,
    "taskIcon": "🎯",
    "buttonText": "Complete"
  }
}
```

### 6. Delete Task

**Mutation:**
```graphql
mutation DeleteTask($taskId: ID!) {
  deleteTask(taskId: $taskId)
}
```

**Variables:**
```json
{
  "taskId": "task-uuid-here"
}
```

## Admin Statistics and Analytics

### 1. Get Task Completion Statistics

**Query:**
```graphql
query GetTaskCompletionStats($input: AdminStatsInput!) {
  adminGetTaskCompletionStats(input: $input) {
    success
    message
    data {
      taskCompletions {
        taskName
        completionCount
      }
      startDate
      endDate
      totalTasks
    }
  }
}
```

**Variables:**
```json
{
  "input": {
    "startDate": "2024-01-01T00:00:00Z",
    "endDate": "2024-01-31T23:59:59Z"
  }
}
```

### 2. Get User Activity Statistics

**Query:**
```graphql
query GetUserActivityStats($input: AdminStatsInput!) {
  adminGetUserActivityStats(input: $input) {
    success
    message
    data {
      dailyCompletions {
        date
        completionCount
      }
      startDate
      endDate
    }
  }
}
```

### 3. Get Tier Distribution

**Query:**
```graphql
query GetTierDistribution {
  adminGetTierDistribution {
    success
    message
    data {
      tierLevel
      tierName
      userCount
      percentage
    }
  }
}
```

### 4. Get Top Users by Points

**Query:**
```graphql
query GetTopUsers($limit: Int) {
  adminGetTopUsers(limit: $limit) {
    userId
    currentTier
    totalPoints
    pointsThisMonth
    tradingVolumeUsd
    activeDaysThisMonth
    cumulativeCashbackUsd
    claimableCashbackUsd
    claimedCashbackUsd
    lastActivityDate
    tierUpgradedAt
    monthlyResetAt
  }
}
```

**Variables:**
```json
{
  "limit": 20
}
```

## System Management Operations

### 1. Reset Monthly Tasks

**Mutation:**
```graphql
mutation ResetMonthlyTasks {
  adminResetMonthlyTasks
}
```

### 2. Recalculate All User Tiers

**Mutation:**
```graphql
mutation RecalculateAllUserTiers {
  adminRecalculateAllUserTiers
}
```

### 3. Get User Tier Info

**Query:**
```graphql
query GetUserTierInfo($input: UserTierInfoInput!) {
  userTierInfo(input: $input) {
    userId
    currentTier
    totalPoints
    pointsThisMonth
    tradingVolumeUsd
    activeDaysThisMonth
    cumulativeCashbackUsd
    claimableCashbackUsd
    claimedCashbackUsd
    lastActivityDate
    tierUpgradedAt
    monthlyResetAt
  }
}
```

**Variables:**
```json
{
  "input": {
    "userId": "user-uuid-here"
  }
}
```

## REST API Endpoints

In addition to GraphQL, some admin operations are available via REST endpoints:

### Health Check
```http
GET /api/activity-cashback/health
X-API-Key: your-internal-api-key
```

**Response:**
```json
{
  "status": "healthy",
  "database": "connected",
  "redis": "connected",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### System Status
```http
GET /api/activity-cashback/status
X-API-Key: your-internal-api-key
```

**Response:**
```json
{
  "system": "operational",
  "background_jobs": "running",
  "last_daily_reset": "2024-01-01T00:00:00Z",
  "last_monthly_reset": "2024-01-01T00:00:00Z"
}
```

### Force Task Reset
```http
POST /api/activity-cashback/admin/reset/{type}
X-API-Key: your-internal-api-key
```

**Parameters:**
- `type`: `daily`, `weekly`, or `monthly`

**Response:**
```json
{
  "message": "daily tasks reset completed",
  "affected_users": 1250,
  "reset_time": "2024-01-01T00:00:00Z"
}
```

### Recalculate User Tiers
```http
POST /api/activity-cashback/admin/recalculate-tiers
X-API-Key: your-internal-api-key
```

**Response:**
```json
{
  "message": "tier recalculation completed",
  "processed_users": 1250,
  "tier_changes": 45
}
```

## Data Types and Validation Rules

### Tier Benefit Constraints
- `tierLevel`: Must be unique, positive integer
- `tierName`: Required, max 50 characters
- `minPoints`: Must be >= 0, should increase with tier level
- `cashbackPercentage`: Decimal between 0 and 1 (0% to 100%)
- `netFee`: Decimal between 0 and 1 (0% to 100%)
- `referredIncentivePercentage`: Decimal, typically 0.05 (5%)

### Task Constraints
- `name.en`: Required, max 100 characters
- `points`: Must be >= 0
- `maxCompletions`: NULL for unlimited, positive integer for limited
- `sortOrder`: Integer for display ordering
- `startDate`/`endDate`: Unix timestamps, endDate must be after startDate

### Category Constraints
- `name`: Must be one of: DAILY, COMMUNITY, TRADING
- `displayName`: Required, max 100 characters
- `sortOrder`: Integer for display ordering

## Error Handling

### Common Error Responses

#### Authentication Error
```json
{
  "errors": [
    {
      "message": "admin access required: invalid or missing API key",
      "extensions": {
        "code": "UNAUTHENTICATED"
      }
    }
  ]
}
```

#### Validation Error
```json
{
  "errors": [
    {
      "message": "validation failed",
      "extensions": {
        "code": "BAD_USER_INPUT",
        "details": {
          "tierLevel": "tier level must be unique",
          "minPoints": "minimum points must be non-negative"
        }
      }
    }
  ]
}
```

#### Not Found Error
```json
{
  "errors": [
    {
      "message": "tier benefit not found",
      "extensions": {
        "code": "NOT_FOUND",
        "id": "123"
      }
    }
  ]
}
```

#### Business Logic Error
```json
{
  "errors": [
    {
      "message": "cannot delete tier benefit: users exist at this tier level",
      "extensions": {
        "code": "BUSINESS_LOGIC_ERROR",
        "affected_users": 25
      }
    }
  ]
}
```

## Practical Examples

### Example 1: Setting Up a Complete Tier System

```graphql
# Step 1: Create tier benefits
mutation CreateBronzeTier {
  createTierBenefit(input: {
    tierLevel: 1
    tierName: "Bronze"
    minPoints: 0
    cashbackPercentage: 0.001
    netFee: 0.0005
    benefitsDescription: "Entry level with basic rewards"
    tierColor: "#CD7F32"
    tierIcon: "🥉"
  }) {
    success
    data { id }
  }
}

mutation CreateSilverTier {
  createTierBenefit(input: {
    tierLevel: 2
    tierName: "Silver"
    minPoints: 1000
    cashbackPercentage: 0.002
    netFee: 0.0008
    benefitsDescription: "Enhanced cashback and reduced fees"
    tierColor: "#C0C0C0"
    tierIcon: "🥈"
  }) {
    success
    data { id }
  }
}

mutation CreateGoldTier {
  createTierBenefit(input: {
    tierLevel: 3
    tierName: "Gold"
    minPoints: 5000
    cashbackPercentage: 0.003
    netFee: 0.001
    benefitsDescription: "Premium benefits with higher cashback"
    tierColor: "#FFD700"
    tierIcon: "🥇"
  }) {
    success
    data { id }
  }
}
```

### Example 2: Creating a Daily Task Set

```graphql
# Step 1: Get category ID for DAILY
query GetDailyCategory {
  adminGetAllCategories(input: {page: 1, pageSize: 10}) {
    data {
      id
      name
      displayName
    }
  }
}

# Step 2: Create daily login task
mutation CreateDailyLogin {
  createTask(input: {
    categoryId: "1"  # Use actual category ID from step 1
    name: {
      en: "Daily Login"
      zh: "每日登录"
    }
    description: "Login daily to earn points"
    frequency: DAILY
    taskIdentifier: DAILY_CHECKIN
    points: 10
    maxCompletions: 1
    resetPeriod: "daily"
    actionTarget: "login"
    verificationMethod: "auto"
    taskIcon: "🎁"
    buttonText: "Claim"
    sortOrder: 1
  }) {
    id
    name { en }
    points
  }
}

# Step 3: Create trading task
mutation CreateTradingTask {
  createTask(input: {
    categoryId: "1"
    name: {
      en: "Complete MEME Trade"
      zh: "完成MEME交易"
    }
    description: "Complete one MEME transaction"
    frequency: DAILY
    taskIdentifier: MEME_TRADE_DAILY
    points: 200
    maxCompletions: 1
    resetPeriod: "daily"
    actionTarget: "memeTrade"
    verificationMethod: "auto"
    taskIcon: "💰"
    buttonText: "Trade"
    sortOrder: 2
  }) {
    id
    name { en }
    points
  }
}
```

### Example 3: Creating Progressive Tasks

```graphql
# Create consecutive check-in task with milestones
mutation CreateConsecutiveCheckin {
  createConsecutiveCheckinTask(input: {
    milestones: [
      {days: 3, points: 50},
      {days: 7, points: 200},
      {days: 15, points: 500},
      {days: 30, points: 1000}
    ]
    categoryId: "1"
    name: {
      en: "Consecutive Check-in Streak"
      zh: "连续签到奖励"
    }
    description: "Earn bonus points for maintaining daily check-in streaks"
    taskIcon: "🔥"
    buttonText: "Check In"
    sortOrder: 1
  }) {
    id
    name { en }
    conditions
  }
}

# Create volume-based trading task
mutation CreateVolumeTask {
  createAccumulatedMEMETradingVolumeTask(input: {
    volumeThreshold: 10000.0
    points: 1000
    categoryId: "3"  # Trading category
    name: {
      en: "High Volume Trader"
      zh: "高交易量交易者"
    }
    description: "Reach $10,000 in MEME trading volume"
    taskIcon: "🚀"
    buttonText: "Trade"
    sortOrder: 1
  }) {
    id
    name { en }
    conditions
  }
}
```

## Testing Guidelines

### 1. Environment Setup
- Use a dedicated testing environment
- Ensure test database is isolated from production
- Configure test API keys different from production

### 2. Testing Authentication
```bash
# Test with valid API key
curl -X POST \
  -H "X-API-Key: test-api-key" \
  -H "Content-Type: application/json" \
  -d '{"query": "query { adminGetAllTasks { id name { en } } }"}' \
  http://localhost:8080/api/dex-agent/admin/graphql

# Test with invalid API key (should return 401)
curl -X POST \
  -H "X-API-Key: invalid-key" \
  -H "Content-Type: application/json" \
  -d '{"query": "query { adminGetAllTasks { id name { en } } }"}' \
  http://localhost:8080/api/dex-agent/admin/graphql
```

### 3. Testing CRUD Operations
```bash
# Test tier benefit creation
curl -X POST \
  -H "X-API-Key: test-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "mutation CreateTierBenefit($input: CreateTierBenefitInput!) { createTierBenefit(input: $input) { success data { id tierName } } }",
    "variables": {
      "input": {
        "tierLevel": 1,
        "tierName": "Test Tier",
        "minPoints": 0,
        "cashbackPercentage": 0.001,
        "netFee": 0.0005
      }
    }
  }' \
  http://localhost:8080/api/dex-agent/admin/graphql
```

### 4. Testing Validation
- Test with missing required fields
- Test with invalid data types
- Test with duplicate tier levels
- Test with negative points/percentages
- Test with invalid task identifiers

### 5. Testing Business Logic
- Verify tier progression logic
- Test task completion constraints
- Verify point calculation accuracy
- Test reset functionality
- Verify cashback calculations

### 6. Performance Testing
- Test with large datasets
- Monitor query performance
- Test concurrent operations
- Verify memory usage during bulk operations

## Best Practices

### 1. Data Management
- Always validate input data before creating/updating
- Use transactions for related operations
- Implement proper error handling and rollback
- Monitor system performance after changes

### 2. Security
- Never expose API keys in client-side code
- Rotate API keys regularly
- Log all admin operations for audit trails
- Implement rate limiting for admin endpoints

### 3. Testing
- Test all CRUD operations thoroughly
- Verify business logic constraints
- Test error scenarios and edge cases
- Use automated testing for regression prevention

### 4. Monitoring
- Monitor API response times
- Track error rates and types
- Monitor system resource usage
- Set up alerts for critical failures

This comprehensive guide provides all the necessary information for frontend developers to integrate with the XBIT Agent Activity Cashback admin APIs. For additional support or questions, refer to the system documentation or contact the backend development team.
